#!/usr/bin/env python3
"""
聊天室系统测试脚本

测试基本功能：用户注册、登录、消息发送、历史查询等。
"""

import sys
import os
import time
import threading
import socket

# 添加shared目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'shared'))

from protocol import Protocol, MessageType
from encryption import get_encryption_handler, EncryptionHandler


class TestClient:
    """测试客户端类"""
    
    def __init__(self, name: str, host: str = 'localhost', port: int = 8888):
        self.name = name
        self.host = host
        self.port = port
        self.socket = None
        self.running = False
        self.messages_received = []
        
        self.encryption_handler = get_encryption_handler()
        self.protocol = Protocol(self.encryption_handler)
    
    def connect(self) -> bool:
        """连接到服务器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            self.running = True
            
            # 启动接收线程
            receive_thread = threading.Thread(target=self.receive_messages, daemon=True)
            receive_thread.start()
            
            return True
        except Exception as e:
            print(f"{self.name}: 连接失败 - {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        self.running = False
        if self.socket:
            self.socket.close()
    
    def receive_messages(self):
        """接收消息线程"""
        while self.running:
            try:
                message_data = self.receive_message()
                if not message_data:
                    break
                
                parsed_message = self.protocol.unpack_message(message_data)
                if parsed_message:
                    msg_type, payload, seq_num = parsed_message
                    self.messages_received.append((msg_type, payload))
                    
            except Exception as e:
                if self.running:
                    print(f"{self.name}: 接收消息错误 - {e}")
                break
    
    def receive_message(self) -> bytes:
        """接收完整消息"""
        try:
            # 接收Header
            header_data = b''
            while len(header_data) < Protocol.HEADER_SIZE:
                chunk = self.socket.recv(Protocol.HEADER_SIZE - len(header_data))
                if not chunk:
                    return None
                header_data += chunk
            
            # 获取消息总长度
            from protocol import get_message_size
            total_size = get_message_size(header_data)
            if not total_size:
                return None
            
            # 接收完整消息
            message_data = header_data
            while len(message_data) < total_size:
                chunk = self.socket.recv(total_size - len(message_data))
                if not chunk:
                    return None
                message_data += chunk
            
            return message_data
            
        except socket.error:
            return None
    
    def send_message(self, message: bytes) -> bool:
        """发送消息"""
        try:
            self.socket.sendall(message)
            return True
        except Exception as e:
            print(f"{self.name}: 发送消息失败 - {e}")
            return False
    
    def register(self, username: str, password: str) -> bool:
        """注册用户"""
        password_hash = EncryptionHandler.hash_password(password)
        message = self.protocol.create_register_request(username, password_hash)
        if not self.send_message(message):
            return False
        
        # 等待响应
        time.sleep(0.5)
        for msg_type, payload in self.messages_received:
            if msg_type == MessageType.REGISTER_RESPONSE:
                return payload.get('success', False)
        return False
    
    def login(self, username: str, password: str) -> bool:
        """登录用户"""
        password_hash = EncryptionHandler.hash_password(password)
        message = self.protocol.create_login_request(username, password_hash)
        if not self.send_message(message):
            return False
        
        # 等待响应
        time.sleep(0.5)
        for msg_type, payload in self.messages_received:
            if msg_type == MessageType.LOGIN_RESPONSE:
                return payload.get('success', False)
        return False
    
    def send_chat(self, username: str, message: str) -> bool:
        """发送聊天消息"""
        chat_msg = self.protocol.create_chat_message(username, message)
        return self.send_message(chat_msg)
    
    def request_history(self, count: int = 10) -> bool:
        """请求历史消息"""
        history_msg = self.protocol.create_history_request(count)
        return self.send_message(history_msg)


def test_basic_functionality():
    """测试基本功能"""
    print("开始测试聊天室基本功能...")
    
    # 创建两个测试客户端
    client1 = TestClient("Client1")
    client2 = TestClient("Client2")
    
    try:
        # 连接到服务器
        print("1. 测试连接...")
        if not client1.connect():
            print("❌ Client1 连接失败")
            return False
        if not client2.connect():
            print("❌ Client2 连接失败")
            return False
        print("✅ 连接成功")
        
        # 测试注册
        print("2. 测试注册...")
        if not client1.register("testuser1", "password123"):
            print("❌ 用户注册失败")
            return False
        if not client2.register("testuser2", "password456"):
            print("❌ 用户注册失败")
            return False
        print("✅ 注册成功")
        
        # 测试登录
        print("3. 测试登录...")
        if not client1.login("testuser1", "password123"):
            print("❌ 用户登录失败")
            return False
        if not client2.login("testuser2", "password456"):
            print("❌ 用户登录失败")
            return False
        print("✅ 登录成功")
        
        # 测试消息发送
        print("4. 测试消息发送...")
        if not client1.send_chat("testuser1", "Hello from user1!"):
            print("❌ 消息发送失败")
            return False
        if not client2.send_chat("testuser2", "Hello from user2!"):
            print("❌ 消息发送失败")
            return False
        
        # 等待消息传播
        time.sleep(1)
        print("✅ 消息发送成功")
        
        # 测试历史消息
        print("5. 测试历史消息...")
        if not client1.request_history(5):
            print("❌ 历史消息请求失败")
            return False
        
        time.sleep(1)
        print("✅ 历史消息请求成功")
        
        print("🎉 所有基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False
    finally:
        client1.disconnect()
        client2.disconnect()


def main():
    """主函数"""
    print("聊天室系统测试")
    print("=" * 50)
    
    # 检查服务器是否运行
    try:
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        test_socket.settimeout(2)
        result = test_socket.connect_ex(('localhost', 8888))
        test_socket.close()
        
        if result != 0:
            print("❌ 服务器未运行，请先启动服务器:")
            print("   cd server && python server.py")
            return
            
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return
    
    # 运行测试
    if test_basic_functionality():
        print("\n✅ 所有测试通过！聊天室系统工作正常。")
    else:
        print("\n❌ 测试失败！请检查系统配置。")


if __name__ == "__main__":
    main()
