"""
数据库操作模块

提供SQLite数据库的初始化、用户管理和消息存储功能。
包含用户注册、登录验证、消息存储和历史查询等功能。
"""

import sqlite3
import threading
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import os


class DatabaseManager:
    """数据库管理器类"""
    
    def __init__(self, db_path: str = "chat.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.lock = threading.Lock()  # 线程锁，确保数据库操作的线程安全
        self._init_database()
    
    def _init_database(self):
        """初始化数据库，创建必要的表"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                # 创建用户表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username TEXT UNIQUE NOT NULL,
                        password_hash TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建消息表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS messages (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sender_username TEXT NOT NULL,
                        encrypted_payload BLOB NOT NULL,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (sender_username) REFERENCES users (username)
                    )
                ''')
                
                # 创建索引以提高查询性能
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_messages_timestamp 
                    ON messages (timestamp DESC)
                ''')
                
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_users_username 
                    ON users (username)
                ''')
                
                conn.commit()
                print("数据库初始化完成")
                
            except sqlite3.Error as e:
                print(f"数据库初始化错误: {e}")
                conn.rollback()
            finally:
                conn.close()
    
    def register_user(self, username: str, password_hash: str) -> Tuple[bool, str]:
        """
        注册新用户
        
        Args:
            username: 用户名
            password_hash: 密码哈希
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                # 检查用户名是否已存在
                cursor.execute("SELECT username FROM users WHERE username = ?", (username,))
                if cursor.fetchone():
                    return False, "用户名已存在"
                
                # 插入新用户
                cursor.execute(
                    "INSERT INTO users (username, password_hash) VALUES (?, ?)",
                    (username, password_hash)
                )
                conn.commit()
                return True, "注册成功"
                
            except sqlite3.Error as e:
                conn.rollback()
                return False, f"注册失败: {e}"
            finally:
                conn.close()
    
    def verify_user(self, username: str, password_hash: str) -> Tuple[bool, str]:
        """
        验证用户登录
        
        Args:
            username: 用户名
            password_hash: 密码哈希
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute(
                    "SELECT password_hash FROM users WHERE username = ?",
                    (username,)
                )
                result = cursor.fetchone()
                
                if not result:
                    return False, "用户不存在"
                
                stored_hash = result[0]
                if stored_hash == password_hash:
                    return True, "登录成功"
                else:
                    return False, "密码错误"
                    
            except sqlite3.Error as e:
                return False, f"登录验证失败: {e}"
            finally:
                conn.close()
    
    def store_message(self, sender_username: str, encrypted_payload: bytes) -> bool:
        """
        存储消息到数据库
        
        Args:
            sender_username: 发送者用户名
            encrypted_payload: 加密的消息载荷
            
        Returns:
            bool: 是否存储成功
        """
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute(
                    "INSERT INTO messages (sender_username, encrypted_payload) VALUES (?, ?)",
                    (sender_username, encrypted_payload)
                )
                conn.commit()
                return True
                
            except sqlite3.Error as e:
                print(f"消息存储失败: {e}")
                conn.rollback()
                return False
            finally:
                conn.close()
    
    def get_recent_messages(self, count: int = 10) -> List[Dict]:
        """
        获取最近的消息
        
        Args:
            count: 要获取的消息数量
            
        Returns:
            List[Dict]: 消息列表，每个消息包含sender_username, encrypted_payload, timestamp
        """
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    SELECT sender_username, encrypted_payload, timestamp 
                    FROM messages 
                    ORDER BY timestamp DESC 
                    LIMIT ?
                ''', (count,))
                
                results = cursor.fetchall()
                
                # 将结果转换为字典列表（按时间正序排列）
                messages = []
                for row in reversed(results):  # 反转以获得时间正序
                    messages.append({
                        'sender_username': row[0],
                        'encrypted_payload': row[1],
                        'timestamp': row[2]
                    })
                
                return messages
                
            except sqlite3.Error as e:
                print(f"获取历史消息失败: {e}")
                return []
            finally:
                conn.close()
    
    def get_user_count(self) -> int:
        """
        获取注册用户总数
        
        Returns:
            int: 用户总数
        """
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute("SELECT COUNT(*) FROM users")
                result = cursor.fetchone()
                return result[0] if result else 0
                
            except sqlite3.Error as e:
                print(f"获取用户数量失败: {e}")
                return 0
            finally:
                conn.close()
    
    def get_message_count(self) -> int:
        """
        获取消息总数
        
        Returns:
            int: 消息总数
        """
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute("SELECT COUNT(*) FROM messages")
                result = cursor.fetchone()
                return result[0] if result else 0
                
            except sqlite3.Error as e:
                print(f"获取消息数量失败: {e}")
                return 0
            finally:
                conn.close()
    
    def close(self):
        """关闭数据库连接（清理资源）"""
        # SQLite连接在每个方法中都会关闭，这里主要是为了接口完整性
        pass
