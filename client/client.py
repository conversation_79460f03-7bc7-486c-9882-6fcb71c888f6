"""
聊天室客户端程序

实现TCP客户端，提供用户注册、登录、发送消息、查看历史等功能。
使用多线程处理用户输入和消息接收，提供美观的命令行界面。
"""

import socket
import threading
import sys
import os
import time
import re
from datetime import datetime

# 添加shared目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), "..", "shared"))

from protocol import Protocol, MessageType
from encryption import get_encryption_handler, EncryptionHandler


# ANSI颜色代码
class Colors:
    """ANSI颜色代码类"""

    RESET = "\033[0m"
    BOLD = "\033[1m"
    DIM = "\033[2m"

    # 前景色
    BLACK = "\033[30m"
    RED = "\033[31m"
    GREEN = "\033[32m"
    YELLOW = "\033[33m"
    BLUE = "\033[34m"
    MAGENTA = "\033[35m"
    CYAN = "\033[36m"
    WHITE = "\033[37m"

    # 亮色
    BRIGHT_BLACK = "\033[90m"
    BRIGHT_RED = "\033[91m"
    BRIGHT_GREEN = "\033[92m"
    BRIGHT_YELLOW = "\033[93m"
    BRIGHT_BLUE = "\033[94m"
    BRIGHT_MAGENTA = "\033[95m"
    BRIGHT_CYAN = "\033[96m"
    BRIGHT_WHITE = "\033[97m"

    # 背景色
    BG_BLACK = "\033[40m"
    BG_RED = "\033[41m"
    BG_GREEN = "\033[42m"
    BG_YELLOW = "\033[43m"
    BG_BLUE = "\033[44m"
    BG_MAGENTA = "\033[45m"
    BG_CYAN = "\033[46m"
    BG_WHITE = "\033[47m"


def clear_screen():
    """清屏"""
    os.system("cls" if os.name == "nt" else "clear")


def print_banner():
    """打印横幅"""
    banner = f"""
{Colors.BRIGHT_CYAN}╔══════════════════════════════════════════════════════════════╗
║                        🚀 聊天室客户端 🚀                    ║
║                     基于Python Socket实现                    ║
╚══════════════════════════════════════════════════════════════╝{Colors.RESET}
"""
    print(banner)


def print_separator(char="─", length=60, color=Colors.BRIGHT_BLACK):
    """打印分隔线"""
    print(f"{color}{char * length}{Colors.RESET}")


def format_timestamp(timestamp_str):
    """格式化时间戳"""
    try:
        # 解析时间戳
        dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
        now = datetime.now()

        # 如果是今天，只显示时间
        if dt.date() == now.date():
            return dt.strftime("%H:%M:%S")
        # 如果是昨天，显示"昨天 时间"
        elif (now.date() - dt.date()).days == 1:
            return f"昨天 {dt.strftime('%H:%M:%S')}"
        # 其他情况显示完整日期
        else:
            return dt.strftime("%m-%d %H:%M")
    except:
        return timestamp_str


def get_username_color(username):
    """根据用户名获取颜色"""
    colors = [
        Colors.BRIGHT_RED,
        Colors.BRIGHT_GREEN,
        Colors.BRIGHT_YELLOW,
        Colors.BRIGHT_BLUE,
        Colors.BRIGHT_MAGENTA,
        Colors.BRIGHT_CYAN,
    ]
    # 使用用户名的哈希值来选择颜色
    color_index = hash(username) % len(colors)
    return colors[color_index]


class ChatClient:
    """聊天室客户端类"""

    def __init__(self, host: str = "localhost", port: int = 8888):
        """
        初始化客户端

        Args:
            host: 服务器主机地址
            port: 服务器端口
        """
        self.host = host
        self.port = port
        self.socket = None
        self.running = False
        self.authenticated = False
        self.username = None
        self.online_users = set()
        self.message_count = 0

        # 初始化组件
        self.encryption_handler = get_encryption_handler()
        self.protocol = Protocol(self.encryption_handler)

        # 线程锁
        self.print_lock = threading.Lock()

        # 检查终端是否支持颜色
        self.color_support = self._check_color_support()

    def _check_color_support(self):
        """检查终端是否支持颜色"""
        return hasattr(sys.stdout, "isatty") and sys.stdout.isatty()

    def _colorize(self, text, color):
        """根据终端支持情况着色文本"""
        if self.color_support:
            return f"{color}{text}{Colors.RESET}"
        return text

    def connect(self) -> bool:
        """
        连接到服务器

        Returns:
            bool: 是否连接成功
        """
        try:
            print(self._colorize("🔗 正在连接服务器...", Colors.YELLOW))
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(5)  # 设置连接超时
            self.socket.connect((self.host, self.port))
            self.socket.settimeout(None)  # 连接后取消超时
            self.running = True

            success_msg = f"✅ 已连接到服务器 {self.host}:{self.port}"
            print(self._colorize(success_msg, Colors.BRIGHT_GREEN))
            return True
        except socket.timeout:
            error_msg = "❌ 连接超时，请检查服务器是否运行"
            print(self._colorize(error_msg, Colors.BRIGHT_RED))
            return False
        except Exception as e:
            error_msg = f"❌ 连接服务器失败: {e}"
            print(self._colorize(error_msg, Colors.BRIGHT_RED))
            return False

    def disconnect(self):
        """断开连接"""
        self.running = False
        if self.socket:
            self.socket.close()
        with self.print_lock:
            print(self._colorize("👋 已断开连接", Colors.YELLOW))

    def start(self):
        """启动客户端"""
        # 清屏并显示横幅
        clear_screen()
        print_banner()

        if not self.connect():
            return

        # 启动消息接收线程
        receive_thread = threading.Thread(target=self.receive_messages, daemon=True)
        receive_thread.start()

        # 显示欢迎信息
        self.print_welcome()

        # 主循环处理用户输入
        try:
            while self.running:
                # 显示输入提示符
                if self.authenticated:
                    prompt = f"{self._colorize(f'[{self.username}]', get_username_color(self.username))} > "
                else:
                    prompt = f"{self._colorize('[未登录]', Colors.BRIGHT_BLACK)} > "

                try:
                    user_input = input(prompt).strip()
                except EOFError:
                    break

                if not user_input:
                    continue

                if not self.handle_user_input(user_input):
                    break

        except KeyboardInterrupt:
            with self.print_lock:
                print(f"\n{self._colorize('👋 正在退出...', Colors.YELLOW)}")
        finally:
            self.disconnect()

    def print_welcome(self):
        """显示欢迎信息"""
        with self.print_lock:
            print()
            print(self._colorize("📋 命令帮助", Colors.BRIGHT_CYAN))
            print_separator()

            commands = [
                ("📝 /register <用户名> <密码>", "注册新用户"),
                ("🔑 /login <用户名> <密码>", "登录账户"),
                ("📜 /history [数量]", "查看历史消息（默认10条）"),
                ("🚪 /quit", "退出程序"),
                ("💬 直接输入文本", "发送聊天消息（需要先登录）"),
                ("❓ /help", "显示帮助信息"),
                ("📊 /status", "显示连接状态"),
            ]

            for cmd, desc in commands:
                cmd_colored = self._colorize(cmd, Colors.BRIGHT_YELLOW)
                desc_colored = self._colorize(desc, Colors.WHITE)
                print(f"  {cmd_colored:<25} - {desc_colored}")

            print_separator()

            if not self.authenticated:
                tip = "💡 提示: 请先注册或登录后开始聊天"
                print(self._colorize(tip, Colors.BRIGHT_MAGENTA))
            else:
                welcome = f"🎉 欢迎回来, {self.username}!"
                print(self._colorize(welcome, Colors.BRIGHT_GREEN))

            print()

    def handle_user_input(self, user_input: str) -> bool:
        """
        处理用户输入

        Args:
            user_input: 用户输入的字符串

        Returns:
            bool: 是否继续运行
        """
        if user_input.startswith("/"):
            return self.handle_command(user_input)
        else:
            # 普通聊天消息
            if self.authenticated:
                self.send_chat_message(user_input)
            else:
                with self.print_lock:
                    print("请先登录后再发送消息。")

        return True

    def handle_command(self, command: str) -> bool:
        """
        处理命令

        Args:
            command: 命令字符串

        Returns:
            bool: 是否继续运行
        """
        parts = command.split()
        cmd = parts[0].lower()

        if cmd == "/quit":
            return False

        elif cmd == "/register":
            if len(parts) != 3:
                with self.print_lock:
                    error_msg = "❌ 用法: /register <用户名> <密码>"
                    print(self._colorize(error_msg, Colors.BRIGHT_RED))
            else:
                with self.print_lock:
                    info_msg = f"📝 正在注册用户: {parts[1]}..."
                    print(self._colorize(info_msg, Colors.YELLOW))
                self.register_user(parts[1], parts[2])

        elif cmd == "/login":
            if len(parts) != 3:
                with self.print_lock:
                    error_msg = "❌ 用法: /login <用户名> <密码>"
                    print(self._colorize(error_msg, Colors.BRIGHT_RED))
            else:
                with self.print_lock:
                    info_msg = f"🔑 正在登录用户: {parts[1]}..."
                    print(self._colorize(info_msg, Colors.YELLOW))
                self.login_user(parts[1], parts[2])

        elif cmd == "/help":
            self.print_welcome()

        elif cmd == "/status":
            self.show_status()

        elif cmd == "/clear":
            clear_screen()
            print_banner()

        elif cmd == "/history":
            if not self.authenticated:
                with self.print_lock:
                    error_msg = "❌ 请先登录"
                    print(self._colorize(error_msg, Colors.BRIGHT_RED))
            else:
                count = 10  # 默认10条
                if len(parts) > 1:
                    try:
                        count = int(parts[1])
                        if count <= 0:
                            raise ValueError("数量必须大于0")
                    except ValueError:
                        with self.print_lock:
                            error_msg = "❌ 历史消息数量必须是正整数"
                            print(self._colorize(error_msg, Colors.BRIGHT_RED))
                        return True
                self.request_history(count)

        else:
            with self.print_lock:
                error_msg = f"❌ 未知命令: {cmd}，输入 /help 查看帮助"
                print(self._colorize(error_msg, Colors.BRIGHT_RED))

        return True

    def show_status(self):
        """显示连接状态"""
        with self.print_lock:
            print()
            print(self._colorize("📊 连接状态", Colors.BRIGHT_CYAN))
            print_separator()

            # 连接状态
            if self.running:
                status = self._colorize("🟢 已连接", Colors.BRIGHT_GREEN)
            else:
                status = self._colorize("🔴 未连接", Colors.BRIGHT_RED)
            print(f"  服务器: {self.host}:{self.port} - {status}")

            # 认证状态
            if self.authenticated:
                auth_status = self._colorize(
                    f"🔑 已登录 ({self.username})", Colors.BRIGHT_GREEN
                )
            else:
                auth_status = self._colorize("🔒 未登录", Colors.BRIGHT_YELLOW)
            print(f"  认证: {auth_status}")

            # 消息统计
            msg_info = self._colorize(
                f"📨 已接收消息: {self.message_count}", Colors.BRIGHT_BLUE
            )
            print(f"  统计: {msg_info}")

            print_separator()
            print()

    def register_user(self, username: str, password: str):
        """注册用户"""
        password_hash = EncryptionHandler.hash_password(password)
        message = self.protocol.create_register_request(username, password_hash)
        self.send_message(message)

    def login_user(self, username: str, password: str):
        """登录用户"""
        password_hash = EncryptionHandler.hash_password(password)
        message = self.protocol.create_login_request(username, password_hash)
        self.send_message(message)

    def send_chat_message(self, message: str):
        """发送聊天消息"""
        if self.authenticated and self.username:
            chat_msg = self.protocol.create_chat_message(self.username, message)
            self.send_message(chat_msg)

    def request_history(self, count: int):
        """请求历史消息"""
        history_msg = self.protocol.create_history_request(count)
        self.send_message(history_msg)

    def send_message(self, message: bytes) -> bool:
        """
        发送消息到服务器

        Args:
            message: 要发送的消息

        Returns:
            bool: 是否发送成功
        """
        try:
            self.socket.sendall(message)
            return True
        except Exception as e:
            with self.print_lock:
                print(f"发送消息失败: {e}")
            return False

    def receive_messages(self):
        """接收服务器消息的线程函数"""
        while self.running:
            try:
                message_data = self.receive_message()
                if not message_data:
                    break

                # 解析消息
                parsed_message = self.protocol.unpack_message(message_data)
                if not parsed_message:
                    continue

                msg_type, payload, _ = parsed_message  # 忽略seq_num
                self.handle_server_message(msg_type, payload)
                self.message_count += 1

            except Exception as e:
                if self.running:
                    with self.print_lock:
                        print(f"接收消息时出错: {e}")
                break

    def receive_message(self) -> bytes:
        """
        接收完整的消息

        Returns:
            bytes: 完整的消息数据，如果连接断开则返回None
        """
        try:
            # 先接收Header
            header_data = b""
            while len(header_data) < Protocol.HEADER_SIZE:
                chunk = self.socket.recv(Protocol.HEADER_SIZE - len(header_data))
                if not chunk:
                    return None
                header_data += chunk

            # 从Header中获取消息总长度
            from protocol import get_message_size

            total_size = get_message_size(header_data)
            if not total_size:
                return None

            # 接收完整消息
            message_data = header_data
            while len(message_data) < total_size:
                chunk = self.socket.recv(total_size - len(message_data))
                if not chunk:
                    return None
                message_data += chunk

            return message_data

        except socket.error:
            return None

    def handle_server_message(self, msg_type: MessageType, payload: dict):
        """处理服务器消息"""
        with self.print_lock:
            if msg_type == MessageType.REGISTER_RESPONSE:
                success = payload.get("success", False)
                message = payload.get("message", "")
                if success:
                    success_msg = f"✅ {message}"
                    print(self._colorize(success_msg, Colors.BRIGHT_GREEN))
                else:
                    error_msg = f"❌ {message}"
                    print(self._colorize(error_msg, Colors.BRIGHT_RED))

            elif msg_type == MessageType.LOGIN_RESPONSE:
                success = payload.get("success", False)
                message = payload.get("message", "")
                if success:
                    self.authenticated = True
                    self.username = payload.get("username")
                    success_msg = f"✅ {message}"
                    print(self._colorize(success_msg, Colors.BRIGHT_GREEN))
                else:
                    error_msg = f"❌ {message}"
                    print(self._colorize(error_msg, Colors.BRIGHT_RED))

            elif msg_type == MessageType.BROADCAST_MESSAGE:
                username = payload.get("username", "")
                message = payload.get("message", "")
                timestamp = payload.get("timestamp", "")

                # 格式化时间戳
                formatted_time = format_timestamp(timestamp)
                time_colored = self._colorize(
                    f"[{formatted_time}]", Colors.BRIGHT_BLACK
                )

                # 用户名着色
                username_colored = self._colorize(
                    username, get_username_color(username)
                )

                # 消息内容
                message_colored = self._colorize(message, Colors.WHITE)

                print(f"{time_colored} {username_colored}: {message_colored}")

            elif msg_type == MessageType.HISTORY_RESPONSE:
                messages = payload.get("messages", [])
                if messages:
                    print()
                    header = f"📜 最近{len(messages)}条消息"
                    print(self._colorize(header, Colors.BRIGHT_CYAN))
                    print_separator()

                    for msg in messages:
                        username = msg.get("username", "")
                        message = msg.get("message", "")
                        timestamp = msg.get("timestamp", "")

                        formatted_time = format_timestamp(timestamp)
                        time_colored = self._colorize(
                            f"[{formatted_time}]", Colors.BRIGHT_BLACK
                        )
                        username_colored = self._colorize(
                            username, get_username_color(username)
                        )
                        message_colored = self._colorize(message, Colors.WHITE)

                        print(f"{time_colored} {username_colored}: {message_colored}")

                    print_separator()
                    print()
                else:
                    no_msg = "📭 暂无历史消息"
                    print(self._colorize(no_msg, Colors.BRIGHT_YELLOW))

            elif msg_type == MessageType.ERROR_NOTIFICATION:
                error_code = payload.get("error_code", "")
                message = payload.get("message", "")
                error_msg = f"⚠️  错误 [{error_code}]: {message}"
                print(self._colorize(error_msg, Colors.BRIGHT_RED))


def main():
    """主函数"""
    # 可以通过命令行参数指定服务器地址
    host = "localhost"
    port = 8888

    if len(sys.argv) > 1:
        host = sys.argv[1]
    if len(sys.argv) > 2:
        try:
            port = int(sys.argv[2])
        except ValueError:
            print("端口号必须是数字")
            return

    client = ChatClient(host, port)
    client.start()


if __name__ == "__main__":
    main()
