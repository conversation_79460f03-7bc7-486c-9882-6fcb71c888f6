"""
聊天室客户端程序

实现TCP客户端，提供用户注册、登录、发送消息、查看历史等功能。
使用多线程处理用户输入和消息接收，提供简单的命令行界面。
"""

import socket
import threading
import sys
import os
from datetime import datetime

# 添加shared目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))

from protocol import Protocol, MessageType
from encryption import get_encryption_handler, EncryptionHandler


class ChatClient:
    """聊天室客户端类"""
    
    def __init__(self, host: str = 'localhost', port: int = 8888):
        """
        初始化客户端
        
        Args:
            host: 服务器主机地址
            port: 服务器端口
        """
        self.host = host
        self.port = port
        self.socket = None
        self.running = False
        self.authenticated = False
        self.username = None
        
        # 初始化组件
        self.encryption_handler = get_encryption_handler()
        self.protocol = Protocol(self.encryption_handler)
        
        # 线程锁
        self.print_lock = threading.Lock()
    
    def connect(self) -> bool:
        """
        连接到服务器
        
        Returns:
            bool: 是否连接成功
        """
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            self.running = True
            print(f"已连接到服务器 {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"连接服务器失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        self.running = False
        if self.socket:
            self.socket.close()
        print("已断开连接")
    
    def start(self):
        """启动客户端"""
        if not self.connect():
            return
        
        # 启动消息接收线程
        receive_thread = threading.Thread(target=self.receive_messages, daemon=True)
        receive_thread.start()
        
        # 显示欢迎信息
        self.print_welcome()
        
        # 主循环处理用户输入
        try:
            while self.running:
                user_input = input().strip()
                if not user_input:
                    continue
                
                if not self.handle_user_input(user_input):
                    break
                    
        except KeyboardInterrupt:
            print("\n正在退出...")
        finally:
            self.disconnect()
    
    def print_welcome(self):
        """显示欢迎信息"""
        with self.print_lock:
            print("\n" + "="*50)
            print("欢迎使用聊天室！")
            print("="*50)
            print("命令说明:")
            print("  /register <用户名> <密码>  - 注册新用户")
            print("  /login <用户名> <密码>     - 登录")
            print("  /history [数量]           - 查看历史消息（默认10条）")
            print("  /quit                     - 退出程序")
            print("  直接输入文本              - 发送聊天消息（需要先登录）")
            print("="*50)
            if not self.authenticated:
                print("请先注册或登录。")
            print()
    
    def handle_user_input(self, user_input: str) -> bool:
        """
        处理用户输入
        
        Args:
            user_input: 用户输入的字符串
            
        Returns:
            bool: 是否继续运行
        """
        if user_input.startswith('/'):
            return self.handle_command(user_input)
        else:
            # 普通聊天消息
            if self.authenticated:
                self.send_chat_message(user_input)
            else:
                with self.print_lock:
                    print("请先登录后再发送消息。")
        
        return True
    
    def handle_command(self, command: str) -> bool:
        """
        处理命令
        
        Args:
            command: 命令字符串
            
        Returns:
            bool: 是否继续运行
        """
        parts = command.split()
        cmd = parts[0].lower()
        
        if cmd == '/quit':
            return False
        
        elif cmd == '/register':
            if len(parts) != 3:
                with self.print_lock:
                    print("用法: /register <用户名> <密码>")
            else:
                self.register_user(parts[1], parts[2])
        
        elif cmd == '/login':
            if len(parts) != 3:
                with self.print_lock:
                    print("用法: /login <用户名> <密码>")
            else:
                self.login_user(parts[1], parts[2])
        
        elif cmd == '/history':
            if not self.authenticated:
                with self.print_lock:
                    print("请先登录。")
            else:
                count = 10  # 默认10条
                if len(parts) > 1:
                    try:
                        count = int(parts[1])
                    except ValueError:
                        with self.print_lock:
                            print("历史消息数量必须是数字。")
                        return True
                self.request_history(count)
        
        else:
            with self.print_lock:
                print(f"未知命令: {cmd}")
        
        return True
    
    def register_user(self, username: str, password: str):
        """注册用户"""
        password_hash = EncryptionHandler.hash_password(password)
        message = self.protocol.create_register_request(username, password_hash)
        self.send_message(message)
    
    def login_user(self, username: str, password: str):
        """登录用户"""
        password_hash = EncryptionHandler.hash_password(password)
        message = self.protocol.create_login_request(username, password_hash)
        self.send_message(message)
    
    def send_chat_message(self, message: str):
        """发送聊天消息"""
        if self.authenticated and self.username:
            chat_msg = self.protocol.create_chat_message(self.username, message)
            self.send_message(chat_msg)
    
    def request_history(self, count: int):
        """请求历史消息"""
        history_msg = self.protocol.create_history_request(count)
        self.send_message(history_msg)
    
    def send_message(self, message: bytes) -> bool:
        """
        发送消息到服务器
        
        Args:
            message: 要发送的消息
            
        Returns:
            bool: 是否发送成功
        """
        try:
            self.socket.sendall(message)
            return True
        except Exception as e:
            with self.print_lock:
                print(f"发送消息失败: {e}")
            return False
    
    def receive_messages(self):
        """接收服务器消息的线程函数"""
        while self.running:
            try:
                message_data = self.receive_message()
                if not message_data:
                    break
                
                # 解析消息
                parsed_message = self.protocol.unpack_message(message_data)
                if not parsed_message:
                    continue
                
                msg_type, payload, seq_num = parsed_message
                self.handle_server_message(msg_type, payload)
                
            except Exception as e:
                if self.running:
                    with self.print_lock:
                        print(f"接收消息时出错: {e}")
                break
    
    def receive_message(self) -> bytes:
        """
        接收完整的消息
        
        Returns:
            bytes: 完整的消息数据，如果连接断开则返回None
        """
        try:
            # 先接收Header
            header_data = b''
            while len(header_data) < Protocol.HEADER_SIZE:
                chunk = self.socket.recv(Protocol.HEADER_SIZE - len(header_data))
                if not chunk:
                    return None
                header_data += chunk
            
            # 从Header中获取消息总长度
            from protocol import get_message_size
            total_size = get_message_size(header_data)
            if not total_size:
                return None
            
            # 接收完整消息
            message_data = header_data
            while len(message_data) < total_size:
                chunk = self.socket.recv(total_size - len(message_data))
                if not chunk:
                    return None
                message_data += chunk
            
            return message_data
            
        except socket.error:
            return None
    
    def handle_server_message(self, msg_type: MessageType, payload: dict):
        """处理服务器消息"""
        with self.print_lock:
            if msg_type == MessageType.REGISTER_RESPONSE:
                success = payload.get('success', False)
                message = payload.get('message', '')
                if success:
                    print(f"✓ {message}")
                else:
                    print(f"✗ {message}")
            
            elif msg_type == MessageType.LOGIN_RESPONSE:
                success = payload.get('success', False)
                message = payload.get('message', '')
                if success:
                    self.authenticated = True
                    self.username = payload.get('username')
                    print(f"✓ {message}")
                else:
                    print(f"✗ {message}")
            
            elif msg_type == MessageType.BROADCAST_MESSAGE:
                username = payload.get('username', '')
                message = payload.get('message', '')
                timestamp = payload.get('timestamp', '')
                print(f"[{timestamp}] {username}: {message}")
            
            elif msg_type == MessageType.HISTORY_RESPONSE:
                messages = payload.get('messages', [])
                if messages:
                    print(f"\n=== 最近{len(messages)}条消息 ===")
                    for msg in messages:
                        username = msg.get('username', '')
                        message = msg.get('message', '')
                        timestamp = msg.get('timestamp', '')
                        print(f"[{timestamp}] {username}: {message}")
                    print("=" * 30)
                else:
                    print("暂无历史消息。")
            
            elif msg_type == MessageType.ERROR_NOTIFICATION:
                error_code = payload.get('error_code', '')
                message = payload.get('message', '')
                print(f"错误 [{error_code}]: {message}")


def main():
    """主函数"""
    # 可以通过命令行参数指定服务器地址
    host = 'localhost'
    port = 8888
    
    if len(sys.argv) > 1:
        host = sys.argv[1]
    if len(sys.argv) > 2:
        try:
            port = int(sys.argv[2])
        except ValueError:
            print("端口号必须是数字")
            return
    
    client = ChatClient(host, port)
    client.start()


if __name__ == "__main__":
    main()
