# 基于Python Socket的多人聊天室

## 项目概述

这是一个基于TCP/IP的客户端-服务器架构的多人聊天室应用，实现了自定义的应用层协议，支持用户注册、登录、发送公共聊天消息、查看近期消息历史，并包含基础的加密与认证机制。

## 功能特性

- **用户认证**: 用户注册和登录，密码使用SHA256哈希
- **实时聊天**: 多用户实时消息广播
- **消息历史**: 查看最近的聊天记录
- **安全通信**: AES加密的消息传输
- **并发处理**: 支持多个客户端同时连接
- **数据持久化**: SQLite数据库存储用户信息和消息

## 技术架构

### 协议设计

#### Header结构 (8字节)
- 协议版本 (1 byte): 当前版本为1
- 消息类型 (1 byte): 见下方消息类型表
- 消息序号 (2 bytes): 网络字节序
- Payload长度 (4 bytes): 网络字节序，指示加密后Payload的字节数

#### 消息类型
- 0x01: 注册请求
- 0x02: 注册响应
- 0x03: 登录请求
- 0x04: 登录响应
- 0x05: 聊天消息
- 0x06: 广播消息
- 0x07: 历史消息请求
- 0x08: 历史消息响应
- 0x09: 错误通知
- 0x0A: 心跳包

#### Payload结构
- 格式: JSON
- 加密: AES对称加密 (Fernet)
- 内容: 根据消息类型包含不同字段

### 数据库设计

#### users表
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### messages表
```sql
CREATE TABLE messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sender_username TEXT NOT NULL,
    encrypted_payload BLOB NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_username) REFERENCES users (username)
);
```

## 环境要求

- Python 3.12+
- cryptography库

## 安装和运行

### 1. 创建虚拟环境
```bash
conda create -n chatroom python=3.12 -y
conda activate chatroom
conda install cryptography -y
```

### 2. 运行服务器
```bash
cd server
python server.py
```
服务器将在localhost:8888上启动

### 3. 运行客户端
```bash
cd client
python client.py
```

## 使用说明

### 客户端命令
- `/register <username> <password>`: 注册新用户
- `/login <username> <password>`: 登录
- `/history [count]`: 查看最近的聊天记录（默认10条）
- `/quit`: 退出程序
- 直接输入文本: 发送聊天消息

### 示例会话
```
欢迎使用聊天室！
请先注册或登录。

> /register alice password123
注册成功！

> /login alice password123
登录成功！欢迎 alice！

> Hello everyone!
[2024-01-01 12:00:00] alice: Hello everyone!

> /history 5
=== 最近5条消息 ===
[2024-01-01 11:58:00] bob: Hi there!
[2024-01-01 12:00:00] alice: Hello everyone!
```

## 安全特性

1. **密码安全**: 密码使用SHA256哈希，不以明文存储或传输
2. **消息加密**: 所有Payload使用AES加密传输
3. **错误处理**: 完善的错误处理机制
4. **连接管理**: 自动处理客户端断开连接

## 测试用例

### 基本功能测试
1. 用户注册和登录
2. 多用户聊天
3. 消息历史查询
4. 错误处理（重复用户名、错误密码等）

### 并发测试
1. 多个客户端同时连接
2. 并发消息发送
3. 客户端异常断开处理

## 项目结构

```
socket/
├── shared/
│   ├── protocol.py        # 协议定义
│   └── encryption.py      # 加密功能
├── server/
│   ├── server.py          # 服务器主程序
│   └── database.py        # 数据库操作
├── client/
│   └── client.py          # 客户端程序
├── requirements.txt       # 依赖列表
└── README.md             # 项目文档
```

## 开发说明

代码遵循以下原则：
- 模块化设计，职责分离
- 完善的错误处理
- 详细的代码注释
- 网络字节序处理
- 线程安全的并发处理

## 快速测试

### 自动化测试
```bash
# 确保服务器正在运行
cd server
python server.py &

# 在另一个终端运行测试
python test_chatroom.py
```

### 手动测试步骤

1. **启动服务器**
```bash
cd server
python server.py
```

2. **启动第一个客户端**
```bash
cd client
python client.py
```

3. **在客户端中执行以下命令**
```
/register alice password123
/login alice password123
Hello everyone!
```

4. **启动第二个客户端并测试多用户聊天**
```bash
cd client
python client.py
```

5. **在第二个客户端中执行**
```
/register bob mypassword
/login bob mypassword
Hi Alice!
/history 5
```

## 技术特点

### 协议实现
- 自定义二进制协议，Header-Payload结构
- 网络字节序处理，确保跨平台兼容性
- 消息序号机制，支持消息追踪
- 完整的消息类型定义

### 安全机制
- SHA256密码哈希，密码不明文传输
- AES对称加密，所有Payload加密传输
- 预共享密钥机制（可扩展为更安全的密钥交换）

### 并发处理
- 服务器端多线程处理客户端连接
- 客户端多线程分离输入和接收
- 线程安全的数据结构和数据库操作

### 数据持久化
- SQLite数据库存储用户信息和消息
- 加密消息存储，保护历史数据
- 索引优化，提高查询性能

## 扩展建议

1. **安全增强**
   - 实现密钥交换协议（如Diffie-Hellman）
   - 添加消息完整性校验（HMAC）
   - 实现用户会话管理

2. **功能扩展**
   - 私聊功能
   - 用户在线状态显示
   - 文件传输支持
   - 聊天室/频道概念

3. **性能优化**
   - 连接池管理
   - 消息缓存机制
   - 数据库连接优化

4. **监控和日志**
   - 详细的日志记录
   - 性能监控指标
   - 错误统计和报警
